#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
百家号关注工具打包脚本
使用标准方法创建发布包
"""

import os
import shutil
import zipfile
import datetime
from pathlib import Path

def clean_directory():
    """清理目录中的临时文件和缓存"""
    print("🧹 清理临时文件...")
    
    # 要清理的文件和目录
    cleanup_items = [
        '__pycache__',
        '*.pyc',
        '*.pyo',
        '*.pyd',
        '.pytest_cache',
        'build',
        'dist',
        '*.egg-info',
        '.coverage',
        'htmlcov',
        '.tox',
        '.cache',
        '.DS_Store',
        'Thumbs.db'
    ]
    
    for item in cleanup_items:
        if '*' in item:
            # 处理通配符
            import glob
            for file_path in glob.glob(item, recursive=True):
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        print(f"  删除文件: {file_path}")
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                        print(f"  删除目录: {file_path}")
                except Exception as e:
                    print(f"  清理失败 {file_path}: {e}")
        else:
            try:
                if os.path.exists(item):
                    if os.path.isfile(item):
                        os.remove(item)
                        print(f"  删除文件: {item}")
                    elif os.path.isdir(item):
                        shutil.rmtree(item)
                        print(f"  删除目录: {item}")
            except Exception as e:
                print(f"  清理失败 {item}: {e}")

def create_batch_files(package_dir):
    """创建批处理文件"""
    print("📝 创建批处理文件...")
    
    # 安装依赖批处理文件
    install_bat = """@echo off
chcp 65001 >nul
echo ========================================
echo 百家号关注工具 - 依赖安装
echo ========================================
echo.
echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址：https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.
echo 正在安装依赖包...
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
if errorlevel 1 (
    echo.
    echo ❌ 依赖安装失败，尝试使用默认源...
    pip install -r requirements.txt
)

echo.
echo ✅ 依赖安装完成！
echo 现在可以运行"启动程序.bat"来使用工具
pause
"""
    
    # 启动程序批处理文件
    start_bat = """@echo off
chcp 65001 >nul
echo ========================================
echo 百家号关注工具 v2.0
echo ========================================
echo.
echo 正在启动程序...
python baidu_follow_dp.py
if errorlevel 1 (
    echo.
    echo ❌ 程序启动失败
    echo 请确保已运行"安装依赖.bat"安装所需依赖
    pause
)
"""
    
    # 写入批处理文件
    with open(os.path.join(package_dir, "安装依赖.bat"), "w", encoding="utf-8") as f:
        f.write(install_bat)
    
    with open(os.path.join(package_dir, "启动程序.bat"), "w", encoding="utf-8") as f:
        f.write(start_bat)

def create_usage_guide(package_dir):
    """创建使用说明文件"""
    print("📖 创建使用说明...")
    
    usage_text = """百家号关注工具 v2.0 使用说明

========================================
🚀 快速开始
========================================

1. 首次使用：
   - 双击运行"安装依赖.bat"安装所需依赖
   - 等待安装完成后，双击"启动程序.bat"启动程序

2. 程序功能：
   - 账号管理：添加、删除百家号账号
   - 批量关注：支持多账号批量关注指定用户
   - 相互关注：账号之间相互关注
   - 多线程：支持多线程并发操作，提高效率

========================================
📋 使用步骤
========================================

1. 账号管理：
   - 点击"添加账号"按钮
   - 在弹出的浏览器中登录百家号
   - 程序会自动获取账号信息并保存

2. 关注操作：
   - 在"目标用户ID"框中输入要关注的用户ID
   - 或点击"批量导入"从文件导入多个用户ID
   - 选择要使用的账号
   - 点击"开始关注"执行操作

3. 相互关注：
   - 在账号列表中选择多个账号
   - 点击"相互关注"让选中账号互相关注

========================================
⚠️ 注意事项
========================================

1. 系统要求：
   - Windows 7/8/10/11
   - Python 3.7或更高版本
   - Chrome浏览器

2. 使用建议：
   - 建议关注间隔设置为3-5秒，避免操作过快
   - 单次批量关注建议不超过100个用户
   - 定期清理浏览器缓存和Cookie

3. 安全提醒：
   - 请勿在公共电脑上保存账号信息
   - 定期更改账号密码
   - 遵守百度平台相关规定

========================================
🔧 故障排除
========================================

1. 程序无法启动：
   - 检查Python是否正确安装
   - 重新运行"安装依赖.bat"
   - 检查防火墙和杀毒软件设置

2. 登录失败：
   - 清除浏览器缓存和Cookie
   - 检查网络连接
   - 尝试手动登录百家号网站

3. 关注失败：
   - 检查目标用户ID是否正确
   - 确认账号是否正常登录
   - 适当增加操作间隔时间

========================================
📞 技术支持
========================================

如遇到问题，请检查以下文件：
- README.md：详细技术文档
- 修改说明.md：版本更新记录

版本：v2.0
更新日期：2025年1月
"""
    
    with open(os.path.join(package_dir, "使用说明.txt"), "w", encoding="utf-8") as f:
        f.write(usage_text)

def create_package():
    """创建发布包"""
    print("📦 开始创建发布包...")
    
    # 获取当前时间戳
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    package_name = f"百家号关注工具_v2.1_final_{timestamp}"
    package_dir = package_name
    
    # 创建包目录
    if os.path.exists(package_dir):
        shutil.rmtree(package_dir)
    os.makedirs(package_dir)
    
    # 需要包含的文件
    files_to_include = [
        "baidu_follow_dp.py",
        "advanced_security.py", 
        "requirements.txt",
        "README.md",
        "修改说明.md"
    ]
    
    # 复制文件
    print("📁 复制程序文件...")
    for file_name in files_to_include:
        if os.path.exists(file_name):
            shutil.copy2(file_name, package_dir)
            print(f"  ✅ {file_name}")
        else:
            print(f"  ⚠️ 文件不存在: {file_name}")
    
    # 创建批处理文件
    create_batch_files(package_dir)
    
    # 创建使用说明
    create_usage_guide(package_dir)
    
    # 创建ZIP压缩包
    print("🗜️ 创建ZIP压缩包...")
    zip_name = f"{package_name}.zip"
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        for root, dirs, files in os.walk(package_dir):
            for file in files:
                file_path = os.path.join(root, file)
                arc_name = os.path.relpath(file_path, package_dir)
                zipf.write(file_path, arc_name)
                print(f"  📄 {arc_name}")
    
    print(f"\n✅ 打包完成！")
    print(f"📁 文件夹: {package_dir}")
    print(f"📦 压缩包: {zip_name}")
    
    return package_dir, zip_name

def build_exe():
    """使用PyInstaller打包成exe文件"""
    print("🔨 开始打包exe文件...")

    # 清理之前的构建文件
    cleanup_items = ['build', 'dist', '*.spec']
    for item in cleanup_items:
        if '*' in item:
            import glob
            for file_path in glob.glob(item):
                try:
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                        print(f"  删除文件: {file_path}")
                except Exception as e:
                    print(f"  清理失败 {file_path}: {e}")
        else:
            try:
                if os.path.exists(item):
                    if os.path.isdir(item):
                        shutil.rmtree(item)
                        print(f"  删除目录: {item}")
            except Exception as e:
                print(f"  清理失败 {item}: {e}")

    # PyInstaller命令
    cmd = [
        'pyinstaller',
        '--onefile',  # 打包成单个exe文件
        '--windowed',  # 不显示控制台窗口
        '--name=百家号关注工具',  # 指定exe文件名
        '--icon=NONE',  # 暂时不使用图标
        '--add-data=baidu_follow_config.json;.',  # 包含配置文件
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.ttk',
        '--hidden-import=tkinter.filedialog',
        '--hidden-import=tkinter.messagebox',
        '--hidden-import=tkinter.scrolledtext',
        '--hidden-import=DrissionPage',
        '--hidden-import=requests',
        '--hidden-import=urllib3',
        '--hidden-import=threading',
        '--hidden-import=queue',
        '--hidden-import=concurrent.futures',
        '--collect-all=DrissionPage',
        'baidu_follow_dp.py'
    ]

    print("执行命令:", ' '.join(cmd))

    # 执行打包命令
    import subprocess
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')

        if result.returncode == 0:
            print("✅ exe打包成功！")

            # 检查生成的文件
            exe_path = os.path.join('dist', '百家号关注工具.exe')
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📁 生成文件: {exe_path}")
                print(f"📏 文件大小: {file_size:.1f} MB")

                # 创建发布目录
                release_dir = "百家号关注工具_exe版本"
                if os.path.exists(release_dir):
                    shutil.rmtree(release_dir)
                os.makedirs(release_dir)

                # 复制exe文件
                shutil.copy2(exe_path, release_dir)

                # 创建使用说明
                usage_text = """百家号关注工具 v2.0 - EXE版本使用说明

========================================
🚀 快速开始
========================================

1. 直接运行：
   - 双击"百家号关注工具.exe"即可启动程序
   - 无需安装Python环境和依赖包

2. 程序功能：
   - 账号管理：添加、删除百家号账号
   - 批量关注：支持多账号批量关注指定用户
   - 相互关注：账号之间相互关注
   - 多线程：支持多线程并发操作，提高效率

========================================
📋 使用步骤
========================================

1. 账号管理：
   - 点击"添加账号"按钮
   - 在弹出的浏览器中登录百家号
   - 程序会自动获取账号信息并保存

2. 关注操作：
   - 在"目标用户ID"框中输入要关注的用户ID
   - 或点击"批量导入"从文件导入多个用户ID
   - 选择要使用的账号
   - 点击"开始关注"执行操作

3. 相互关注：
   - 在账号列表中选择多个账号
   - 点击"相互关注"让选中账号互相关注

========================================
⚠️ 注意事项
========================================

1. 系统要求：
   - Windows 7/8/10/11
   - Chrome浏览器

2. 使用建议：
   - 建议关注间隔设置为3-5秒，避免操作过快
   - 单次批量关注建议不超过100个用户
   - 定期清理浏览器缓存和Cookie

3. 安全提醒：
   - 请勿在公共电脑上保存账号信息
   - 定期更改账号密码
   - 遵守百度平台相关规定

========================================
🔧 故障排除
========================================

1. 程序无法启动：
   - 检查防火墙和杀毒软件设置
   - 确保Chrome浏览器已安装
   - 以管理员身份运行程序

2. 登录失败：
   - 清除浏览器缓存和Cookie
   - 检查网络连接
   - 尝试手动登录百家号网站

3. 关注失败：
   - 检查目标用户ID是否正确
   - 确认账号是否正常登录
   - 适当增加操作间隔时间

版本：v2.0 EXE版本
更新日期：2025年1月
"""

                with open(os.path.join(release_dir, "使用说明.txt"), "w", encoding="utf-8") as f:
                    f.write(usage_text)

                print(f"📁 发布目录: {release_dir}")
                return release_dir
            else:
                print("❌ 未找到生成的exe文件")
                return None
        else:
            print("❌ exe打包失败！")
            print("错误输出:", result.stderr)
            return None

    except Exception as e:
        print(f"❌ 打包过程出错: {e}")
        return None

def main():
    """主函数"""
    print("=" * 50)
    print("🔧 百家号关注工具 - 完整打包脚本")
    print("=" * 50)
    print()

    try:
        # 清理临时文件
        clean_directory()
        print()

        # 创建源码版本包
        print("📦 创建源码版本...")
        package_dir, zip_name = create_package()
        print()

        # 打包exe文件
        print("🔨 创建EXE版本...")
        release_dir = build_exe()

        print()
        print("🎉 打包完成！")
        print()
        print("📋 发布清单：")
        print(f"  📁 源码版本目录: {package_dir}")
        print(f"  📦 源码版本压缩包: {zip_name}")
        if release_dir:
            print(f"  📁 EXE版本目录: {release_dir}")
            print(f"  🚀 可执行文件: {release_dir}/百家号关注工具.exe")
        print()
        print("🚀 使用方法：")
        print("  源码版本：解压后运行'安装依赖.bat'，然后运行'启动程序.bat'")
        print("  EXE版本：直接双击exe文件即可运行，无需安装Python环境")

    except Exception as e:
        print(f"❌ 打包失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
