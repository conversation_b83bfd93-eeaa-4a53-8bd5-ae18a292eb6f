百家号关注工具 v2.0 - EXE版本使用说明

========================================
🚀 快速开始
========================================

1. 直接运行：
   - 双击"百家号关注工具.exe"即可启动程序
   - 无需安装Python环境和依赖包

2. 程序功能：
   - 账号管理：添加、删除百家号账号
   - 批量关注：支持多账号批量关注指定用户
   - 相互关注：账号之间相互关注
   - 多线程：支持多线程并发操作，提高效率
   - 失败记录：自动记录关注失败的账号并生成Excel报告

========================================
📋 使用步骤
========================================

1. 账号管理：
   - 点击"添加账号"按钮
   - 在弹出的浏览器中登录百家号
   - 程序会自动获取账号信息并保存

2. 关注操作：
   - 在"目标用户ID"框中输入要关注的用户ID
   - 或点击"批量导入"从文件导入多个用户ID
   - 选择要使用的账号
   - 点击"开始关注"执行操作

3. 相互关注：
   - 在账号列表中选择多个账号
   - 点击"相互关注"让选中账号互相关注

4. 失败记录：
   - 关注操作完成后，如有失败会自动生成Excel报告
   - 报告包含失败账号信息、失败原因和统计汇总
   - 可根据报告识别需要重新获取Cookie的账号

========================================
⚠️ 注意事项
========================================

1. 系统要求：
   - Windows 7/8/10/11
   - Chrome浏览器

2. 使用建议：
   - 建议关注间隔设置为3-5秒，避免操作过快
   - 单次批量关注建议不超过100个用户
   - 定期清理浏览器缓存和Cookie

3. 安全提醒：
   - 请勿在公共电脑上保存账号信息
   - 定期更改账号密码
   - 遵守百度平台相关规定

========================================
🔧 故障排除
========================================

1. 程序无法启动：
   - 检查防火墙和杀毒软件设置
   - 确保Chrome浏览器已安装
   - 以管理员身份运行程序

2. 登录失败：
   - 清除浏览器缓存和Cookie
   - 检查网络连接
   - 尝试手动登录百家号网站

3. 关注失败：
   - 检查目标用户ID是否正确
   - 确认账号是否正常登录
   - 适当增加操作间隔时间

版本：v2.0 EXE版本
更新日期：2025年1月
