# 百度百家号关注工具 v2.0

基于DrissionPage的百家号自动关注工具，支持智能账号管理和多线程批量关注功能。

## 文件说明

- `baidu_follow_dp.py` - 主程序，带UI界面的关注工具
- `7513860820225327143.txt` - Cookie数据文件示例
- `requirements.txt` - Python依赖包

## 🚀 新版本特点

### 智能账号管理
- **Cookie文件夹批量处理** - 选择整个文件夹一键处理所有Cookie
- **智能ID缓存** - 自动读取已保存的账号ID，无需重复登录
- **实时显示** - 账号信息获取过程实时显示在列表中
- **容错性强** - 支持多种Cookie文件格式，提高成功率
- **超快速度** - 并行获取账号信息，大幅提升处理速度

### 多线程关注系统
- **相互关注** - 所有账号之间自动互相关注
- **批量关注** - 所有账号关注指定的ID列表
- **多线程处理** - 支持1-20个线程并发，默认10个线程
- **进度追踪** - 实时显示关注进度和成功率

### 文件管理优化
- **自动重命名** - Cookie文件自动重命名为账号名称
- **信息持久化** - 账号ID和名称自动保存到Cookie文件
- **智能跳过** - 已有信息的账号自动跳过，节省时间

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

```bash
python baidu_follow_dp.py
```

### 操作步骤

1. **启动程序** - 运行上述命令打开UI界面
2. **选择Cookie文件** - 默认已选择当前目录的Cookie文件
3. **获取账号ID** - 点击"一键获取账号ID"按钮
4. **关注操作** - 输入目标ID进行单个或批量关注
5. **查看日志** - 在日志区域查看操作结果

## 工作原理

1. **自动登录** - 使用Cookie文件自动登录百家号
2. **ID提取** - 从账号设置页面自动提取百家号ID
3. **API关注** - 使用验证成功的API接口进行关注操作
4. **结果反馈** - 实时显示操作结果和错误信息

## 注意事项

- **Cookie有效性** - Cookie可能会过期，需要定期更新
- **浏览器要求** - 需要安装Chrome浏览器
- **网络连接** - 确保网络连接正常
- **使用规范** - 请遵守相关法律法规和网站服务条款

## 版本信息

- v1.0 - 基于DrissionPage的关注工具，支持UI界面操作
